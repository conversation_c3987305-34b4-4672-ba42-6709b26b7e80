export interface EntityType {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  parentCategoryId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
}

export interface Feature {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  youtube?: string;
  facebook?: string;
  instagram?: string;
  discord?: string;
  [key: string]: string | undefined;
}

export interface Submitter {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string | null;
  user_metadata: {
    username?: string | null;
    display_name?: string | null;
    profile_picture_url?: string | null;
    internal_user_id?: string;
  };
}

// Updated details interface based on actual API response
export interface EntityDetails {
  entityId: string;
  learningCurve?: 'LOW' | 'MEDIUM' | 'HIGH';
  targetAudience?: string[];
  keyFeatures?: string[];
  useCases?: string[];
  pricingModel?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME';
  priceRange?: string;
  pricingDetails?: string;
  pricingUrl?: string;
  hasFreeTier?: boolean;
  integrations?: string[];
  supportedLanguages?: string[];
  [key: string]: unknown; // Allow other dynamic fields
}

export interface Entity {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  websiteUrl: string;
  logoUrl: string | null;
  documentationUrl?: string;
  contactUrl?: string;
  privacyPolicyUrl?: string;
  foundedYear?: number;
  entityType: EntityType;
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  avgRating: number;
  reviewCount: number;
  status: string; // e.g., 'ACTIVE', 'PENDING'
  socialLinks?: SocialLinks;
  submitter?: Submitter;
  legacyId?: string | null;
  createdAt: string;
  updatedAt: string;
  details?: EntityDetails;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedEntities {
  data: Entity[];
  meta: PaginationMeta;
}

// Interface for query parameters for fetching entities
export interface GetEntitiesParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
  categoryIds?: string[];
  tagIds?: string[];
  entityTypeIds?: string[];
  featureIds?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: string;
}