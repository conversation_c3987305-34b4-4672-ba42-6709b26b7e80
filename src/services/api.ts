// Defines the base URL for the API from an environment variable
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';

// Import the Session type from Supabase
import { Session as SupabaseSession, User as SupabaseUser } from '@supabase/supabase-js';

// Use the same path as in AuthContext.tsx if it works there
import { PrismaUserProfile, LoginCredentials, RegisterData } from '@/types/user'; // CORRECTED IMPORT PATH & Added LoginCredentials, RegisterData
import { PaginatedEntities, GetEntitiesParams, Entity, EntityType, Category, Tag, Feature } from '@/types/entity'; // Added import
import { Review, PaginatedReviews, CreateReviewPayload } from '@/types/review'; // Updated to PaginatedReviews

export interface AuthResponse {
  message: string;
  user?: SupabaseUser; // Use SupabaseUser type
  session?: SupabaseSession; // Use SupabaseSession type
  error?: string;
  details?: Record<string, unknown>; // For validation errors or other details
}

/**
 * Logs in a user.
 * @param credentials - The user's email and password, as an object.
 * @returns A promise that resolves to the server's response.
 */
export const loginUser = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // console.log("[API] Logging in user with email:", credentials.email);
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, { 
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const responseData = await response.json(); // Try to parse JSON regardless of status
    // console.log("[API] Login user response status:", response.status);

    if (!response.ok) {
      // console.error("[API] Login user error response data:", responseData);
      const errorMessage = responseData.message || `Login failed with status: ${response.status}`;
      console.error('[api.ts] Login API Error (v1.1):', responseData);
      const errorToThrow = new Error(errorMessage) as Error & { details?: unknown; statusCode?: number };
      errorToThrow.details = responseData;
      errorToThrow.statusCode = response.status;
      throw errorToThrow;
    }
    // Ensure the successful response matches AuthResponse structure, especially session and user
    return {
      message: responseData.message || "Login successful",
      user: responseData.user, 
      session: responseData.session,
    } as AuthResponse; 
  } catch (error) {
    if (error instanceof Error && 'statusCode' in error) {
        // This is an error we threw from the !response.ok block, re-throw it
        console.error('[api.ts] Re-throwing API error in loginUser (v1.1):', error); // DEBUG v1.1
        throw error;
    }
    console.error('[api.ts] Network or other error in loginUser (v1.1):', error); // DEBUG v1.1
    let message = "An unexpected network error occurred during login.";
    if (error instanceof Error) {
        message = error.message;
    }
    // For unexpected errors, throw a generic error object that form handlers can catch
    const genericError = new Error(message) as Error & { isNetworkError?: boolean };
    genericError.isNetworkError = true; // Flag it as a network/unexpected error
    throw genericError;
  }
};

/**
 * Registers a new user.
 * @param userData - The user's data for registration.
 * @returns A promise that resolves to the server's response.
 */
export const registerUser = async (userData: RegisterData): Promise<AuthResponse> => {
  // console.log("[API] Registering user with data:", {
  //   email: userData.email,
  //   password: userData.password ? '[PRESENT]' : '[ABSENT]',
  //   displayName: userData.displayName,
  // });
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, { // Ensure this endpoint matches your backend
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const responseData = await response.json(); // Try to parse JSON regardless of status
    // console.log("[API] Register user response status:", response.status);

    if (!response.ok) {
      // console.error("[API] Register user error response data:", responseData);
      const errorMessage = responseData.message || `Registration failed with status: ${response.status}`;
      console.error('[api.ts] Registration API Error:', responseData);
      const errorToThrow = new Error(errorMessage) as Error & { details?: unknown; statusCode?: number };
      errorToThrow.details = responseData;
      errorToThrow.statusCode = response.status;
      throw errorToThrow;
    }
    // Ensure the successful response matches AuthResponse structure
    return {
      message: responseData.message || "Registration successful",
      user: responseData.user, // May not be returned on signup, depending on backend
      session: responseData.session, // May not be returned on signup, depending on backend
    } as AuthResponse;
  } catch (error) {
    if (error instanceof Error && 'statusCode' in error) {
        // This is an error we threw from the !response.ok block, re-throw it
        console.error('[api.ts] Re-throwing API error in registerUser:', error);
        throw error;
    }
    console.error('[api.ts] Network or other error in registerUser:', error); // DEBUG
    let message = "An unexpected network error occurred during registration.";
    if (error instanceof Error) {
        message = error.message;
    }
    const genericError = new Error(message) as Error & { isNetworkError?: boolean };
    genericError.isNetworkError = true;
    throw genericError;
  }
};

// syncUserProfileAPI modified to accept session directly
export const syncUserProfileAPI = async (session: SupabaseSession | null): Promise<PrismaUserProfile> => {
  if (!session || !session.access_token) {
    throw new Error("No session or access token provided for profile sync.");
  }

  try {
    const response = await fetch(`${API_BASE_URL}/auth/sync-profile`, { // Corrected Endpoint
      method: 'POST', // Corrected Method
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      // body: JSON.stringify({}), // Backend might not require a body if JWT is sufficient
    });

    if (!response.ok) {
      const errorBody = await response.text(); 
      throw new Error(`Failed to sync user profile. Status: ${response.status}. Body: ${errorBody}`);
    }

    const data: PrismaUserProfile = await response.json();
    return data;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error("[API] Error in syncUserProfileAPI:", errorMessage);
    throw error;
  }
};

/**
 * Fetches a paginated list of entities from the backend.
 * @param params - Query parameters for filtering, sorting, and pagination.
 * @param token - Optional access token for authenticated requests (if needed for entities).
 * @returns A promise that resolves to the paginated list of entities.
 */
export const getEntities = async (
  params?: GetEntitiesParams,
  token?: string | null,
): Promise<PaginatedEntities> => {
  const queryParams = new URLSearchParams();

  if (params) {
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.searchTerm) queryParams.append('searchTerm', params.searchTerm);
    if (params.categoryIds) {
      params.categoryIds.forEach((id) => queryParams.append('categoryIds', id));
    }
    if (params.tagIds) {
      params.tagIds.forEach((id) => queryParams.append('tagIds', id));
    }
    if (params.entityTypeIds) {
      params.entityTypeIds.forEach((id) => queryParams.append('entityTypeIds', id));
    }
    if (params.featureIds) {
      params.featureIds.forEach((id) => queryParams.append('featureIds', id));
    }
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.status) queryParams.append('status', params.status);
    if (params.submitterId) queryParams.append('submitterId', params.submitterId);
    if (params.entityTypeId) queryParams.append('entityTypeId', params.entityTypeId);

    // Date filters
    if (params.createdAtFrom) queryParams.append('createdAtFrom', params.createdAtFrom);
    if (params.createdAtTo) queryParams.append('createdAtTo', params.createdAtTo);

    // Boolean filters
    if (params.hasFreeTier !== undefined) queryParams.append('hasFreeTier', params.hasFreeTier.toString());
    if (params.apiAccess !== undefined) queryParams.append('apiAccess', params.apiAccess.toString());

    // Array filters - only add if array exists, has length, and contains non-empty values
    if (params.employeeCountRanges && Array.isArray(params.employeeCountRanges) && params.employeeCountRanges.length > 0) {
      const validRanges = params.employeeCountRanges.filter(range => range && range.trim());
      if (validRanges.length > 0) {
        validRanges.forEach((range) => queryParams.append('employeeCountRanges', range));
      }
    }
    if (params.fundingStages && Array.isArray(params.fundingStages) && params.fundingStages.length > 0) {
      const validStages = params.fundingStages.filter(stage => stage && stage.trim());
      if (validStages.length > 0) {
        validStages.forEach((stage) => queryParams.append('fundingStages', stage));
      }
    }
    if (params.pricingModels && Array.isArray(params.pricingModels) && params.pricingModels.length > 0) {
      const validModels = params.pricingModels.filter(model => model && model.trim());
      if (validModels.length > 0) {
        validModels.forEach((model) => queryParams.append('pricingModels', model));
      }
    }
    if (params.priceRanges && Array.isArray(params.priceRanges) && params.priceRanges.length > 0) {
      const validRanges = params.priceRanges.filter(range => range && range.trim());
      if (validRanges.length > 0) {
        validRanges.forEach((range) => queryParams.append('priceRanges', range));
      }
    }
    if (params.integrations && Array.isArray(params.integrations) && params.integrations.length > 0) {
      const validIntegrations = params.integrations.filter(integration => integration && integration.trim());
      if (validIntegrations.length > 0) {
        validIntegrations.forEach((integration) => queryParams.append('integrations', integration));
      }
    }
    if (params.platforms && Array.isArray(params.platforms) && params.platforms.length > 0) {
      const validPlatforms = params.platforms.filter(platform => platform && platform.trim());
      if (validPlatforms.length > 0) {
        validPlatforms.forEach((platform) => queryParams.append('platforms', platform));
      }
    }
    if (params.targetAudience && Array.isArray(params.targetAudience) && params.targetAudience.length > 0) {
      const validAudience = params.targetAudience.filter(audience => audience && audience.trim());
      if (validAudience.length > 0) {
        validAudience.forEach((audience) => queryParams.append('targetAudience', audience));
      }
    }

    // Location search
    if (params.locationSearch) queryParams.append('locationSearch', params.locationSearch);
  }

  const fullUrl = `${API_BASE_URL}/entities?${queryParams.toString()}`;
  // console.log("[API] Fetching entities from:", fullUrl); // Optional: Log the URL

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        // 'Content-Type': 'application/json', // Removed for GET
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      let errorData: Record<string, unknown> = {};
      let responseText = '';
      try {
        responseText = await response.text();
        if (responseText) { // Attempt to parse only if responseText is not empty
          errorData = JSON.parse(responseText);
        }
      } catch {
        // JSON parsing failed or responseText was initially empty.
        // errorData will remain {} or be partially filled if parse started but failed.
        // console.error('[API] getEntities: Failed to parse error response as JSON. Raw text:', responseText, 'Error:', e);
      }

      const status = response.status;
      const messageFromServer = errorData?.message;
      let errorMessage: string;

      if (typeof messageFromServer === 'string' && messageFromServer.trim() !== "") {
        errorMessage = messageFromServer;
      } else {
        errorMessage = `Request failed with status ${status}.`;
        // If the server message wasn't usable, and we have responseText, it's likely more informative.
        if (responseText) {
          errorMessage += ` Response: ${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}`;
        } else {
          // Server message not usable, no responseText either.
          errorMessage += ` The response body was empty or not valid text.`;
        }
      }
      
      console.error(`[API Error getEntities] Status: ${status}. Message: ${errorMessage}. Details:`, errorData, `Raw Response: ${responseText.substring(0,200)}`);

      const errorToThrow = new Error(errorMessage) as Error & {
        statusCode?: number;
        errorDetails?: Record<string, unknown>;
        responseText?: string;
      };
      errorToThrow.statusCode = status;
      errorToThrow.errorDetails = errorData; // Store full parsed error data
      errorToThrow.responseText = responseText; // Store full raw response text
      throw errorToThrow;
    }

    return response.json();
  } catch (error: unknown) {
    // If the error object has a statusCode property, it means we processed it from a non-ok response
    if (error && typeof error === 'object' && 'statusCode' in error && typeof (error as { statusCode: unknown }).statusCode === 'number') {
      // console.error('[API Catch getEntities] Re-throwing processed API error:', error.message);
      throw error; // Re-throw the original error object with all its details
    }
    // For other types of errors (e.g., network issues, or errors thrown before response.ok is checked)
    const originalMessage = error instanceof Error ? error.message : String(error);
    console.error('[API Catch getEntities] Network or other error, wrapping:', originalMessage);
    throw new Error(`Network error or unexpected issue fetching entities: ${originalMessage}`);
  }
};

/**
 * Fetches a single entity by its ID from the backend.
 * @param id - The ID of the entity to fetch.
 * @param token - Optional access token for authenticated requests.
 * @returns A promise that resolves to the entity.
 */
export const getEntityById = async (
  id: string,
  token?: string | null,
): Promise<Entity> => {
  const response = await fetch(`${API_BASE_URL}/entities/${id}`, {
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch entity and parse error response' }));
    console.error(`Error fetching entity ${id}:`, errorData);
    throw new Error(
      errorData.message || `Failed to fetch entity ${id}. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches reviews for a specific entity by its ID from the backend.
 * @param entityId - The ID of the entity for which to fetch reviews.
 * @param token - Optional access token for authenticated requests.
 * @param page - Optional page number for pagination.
 * @param limit - Optional limit for pagination.
 * @returns A promise that resolves to a list of reviews.
 */
export const getReviewsByEntityId = async (
  entityId: string,
  token?: string | null,
  page?: number,
  limit?: number,
): Promise<PaginatedReviews> => { 
  const queryParams = new URLSearchParams();
  if (page) queryParams.append('page', page.toString());
  if (limit) queryParams.append('limit', limit.toString());
  // Add other query params if your backend supports them (e.g., sortBy, sortOrder for reviews)

  const response = await fetch(`${API_BASE_URL}/entities/${entityId}/reviews?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch reviews and parse error response' }));
    console.error(`Error fetching reviews for entity ${entityId}:`, errorData);
    throw new Error(
      errorData.message || `Failed to fetch reviews for entity ${entityId}. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

// Function to submit a new review
export const submitReview = async (
  payload: CreateReviewPayload,
  token: string, // Assuming review submission requires authentication
): Promise<Review> => { // Assuming the backend returns the created review
  const response = await fetch(`${API_BASE_URL}/entities/${payload.entityId}/reviews`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({
      rating: payload.rating,
      title: payload.title,
      text: payload.text,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to submit review and parse error response' }));
    console.error('Error submitting review:', errorData);
    throw new Error(
      errorData.message || `Failed to submit review. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches all available entity types from the backend.
 * @param token - Optional access token for authenticated requests.
 * @returns A promise that resolves to a list of entity types.
 */
export const getEntityTypes = async (token?: string | null): Promise<EntityType[]> => {
  const response = await fetch(`${API_BASE_URL}/entity-types`, { // Assuming endpoint is /entity-types
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch entity types and parse error response' }));
    console.error('Error fetching entity types:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch entity types. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

/**
 * Fetches all public categories from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of categories.
 */
export const getCategories = async (token?: string | null): Promise<Category[]> => {
  const response = await fetch(`${API_BASE_URL}/categories`, { // Assuming public endpoint /categories
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch categories and parse error response' }));
    console.error('Error fetching categories:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch categories. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

/**
 * Fetches all public tags from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of tags.
 */
export const getTags = async (token?: string | null): Promise<Tag[]> => {
  const response = await fetch(`${API_BASE_URL}/tags`, { // Assuming public endpoint /tags
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch tags and parse error response' }));
    console.error('Error fetching tags:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch tags. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches all public features from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of features.
 */
export const getFeatures = async (token?: string | null): Promise<Feature[]> => {
  const response = await fetch(`${API_BASE_URL}/features`, { // Assuming public endpoint /features
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch features and parse error response' }));
    console.error('Error fetching features:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch features. Status: ${response.status}`,
    );
  }
  return response.json();
};

// Add more functions here for signup, fetching entities, etc. as we build features.
// e.g., export async function getEntities(params) { ... fetch(`${API_BASE_URL}/entities?${params}`) ... }