'use client';

import React, { useEffect, useState, useCallback, useMemo, useRef, Suspense } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import ResourceCard from '@/components/resource/ResourceCard';
import SmartFilterSection from '@/components/browse/SmartFilterSection';
import ActiveFilters from '@/components/browse/ActiveFilters';
import AdvancedFilters from '@/components/browse/AdvancedFilters';
import { getEntities, getEntityTypes, getCategories, getTags, getFeatures } from '@/services/api';
import { Entity, EntityType, Category, Tag, Feature, PaginationMeta, GetEntitiesParams } from '@/types/entity';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SearchIcon, Filter } from 'lucide-react';
import ResourceCardSkeleton from '@/components/resource/ResourceCardSkeleton';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";

const ITEMS_PER_PAGE = 9; // Adjusted for potentially larger cards or 3-column layout

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Date Added' },
  { value: 'averageRating', label: 'Rating' },
  // { value: 'popularity', label: 'Popularity' }, // Removed due to backend error
  // Add more options as needed, e.g., popularity, reviewCount
];



function BrowsePageContent() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { session } = useAuth();

  const [entities, setEntities] = useState<Entity[]>([]);
  const [allEntityTypes, setAllEntityTypes] = useState<EntityType[]>([]); // To store all available entity types
  const [allCategories, setAllCategories] = useState<Category[]>([]); // New state for categories
  const [allTags, setAllTags] = useState<Tag[]>([]); // New state for tags
  const [allFeatures, setAllFeatures] = useState<Feature[]>([]); // New state for features
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingFilters, setIsLoadingFilters] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false); // For infinite scroll



  // State for mobile filter sidebar visibility
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Note: Filter counts removed - using clean filter display without counts
  // In the future, we could add a separate API endpoint to get filter counts if needed

  // Derived state from URL search parameters
  const searchTerm = useMemo(() => searchParams?.get('searchTerm') || '', [searchParams]);
  const selectedEntityTypeIds = useMemo(() => searchParams?.getAll('entityTypeIds') || [], [searchParams]);
  const selectedCategoryIds = useMemo(() => searchParams?.getAll('categoryIds') || [], [searchParams]); // New
  const selectedTagIds = useMemo(() => searchParams?.getAll('tagIds') || [], [searchParams]); // New
  const selectedFeatureIds = useMemo(() => searchParams?.getAll('featureIds') || [], [searchParams]); // New for features
  const currentPage = useMemo(() => parseInt(searchParams?.get('page') || '1', 10), [searchParams]);
  const sortBy = useMemo(() => searchParams?.get('sortBy') || 'createdAt', [searchParams]); // Default to createdAt
  const sortOrder = useMemo(() => (searchParams?.get('sortOrder') || 'desc') as 'asc' | 'desc', [searchParams]); // Default to desc for createdAt

  // Advanced filter state from URL
  const hasFreeTier = useMemo(() => searchParams?.get('hasFreeTier') === 'true' ? true : undefined, [searchParams]);
  const apiAccess = useMemo(() => searchParams?.get('apiAccess') === 'true' ? true : undefined, [searchParams]);
  const employeeCountRanges = useMemo(() => {
    // Always use getAll to ensure we get an array, even for single values
    const values = searchParams?.getAll('employeeCountRanges') || [];
    console.log('[DEBUG] employeeCountRanges raw values from URL:', values);
    const validValues = values.filter(v => v && v.trim());
    console.log('[DEBUG] employeeCountRanges after filtering:', validValues);
    const result = validValues.length > 0 ? validValues : undefined;
    console.log('[DEBUG] employeeCountRanges final result:', result);
    return result;
  }, [searchParams]);
  const fundingStages = useMemo(() => {
    const values = searchParams?.getAll('fundingStages') || [];
    console.log('[DEBUG] fundingStages raw values from URL:', values);
    const validValues = values.filter(v => v && v.trim());
    console.log('[DEBUG] fundingStages after filtering:', validValues);
    const result = validValues.length > 0 ? validValues : undefined;
    console.log('[DEBUG] fundingStages final result:', result);
    return result;
  }, [searchParams]);
  const pricingModels = useMemo(() => {
    const values = searchParams?.getAll('pricingModels') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const priceRanges = useMemo(() => {
    const values = searchParams?.getAll('priceRanges') || [];
    const validValues = values.filter(v => v && v.trim());
    return validValues.length > 0 ? validValues : undefined;
  }, [searchParams]);
  const createdAtFrom = useMemo(() => searchParams?.get('createdAtFrom') || undefined, [searchParams]);
  const createdAtTo = useMemo(() => searchParams?.get('createdAtTo') || undefined, [searchParams]);
  const locationSearch = useMemo(() => searchParams?.get('locationSearch') || undefined, [searchParams]);

  // Local state for the search input field for better UX (e.g., debounce or submit on enter)
  const [localSearchInput, setLocalSearchInput] = useState(searchTerm);

  // Ref for the element that triggers loading more items
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Effect to update local search input when URL searchTerm changes (e.g., back/forward navigation)
  useEffect(() => {
    setLocalSearchInput(searchTerm);
  }, [searchTerm]);

  const fetchFilterData = useCallback(async () => {
    setIsLoadingFilters(true);
    try {
      const [typesResult, categoriesResult, tagsResult, featuresResult] = await Promise.all([
        allEntityTypes.length === 0 ? getEntityTypes(session?.access_token) : Promise.resolve(allEntityTypes),
        allCategories.length === 0 ? getCategories(session?.access_token) : Promise.resolve(allCategories),
        allTags.length === 0 ? getTags(session?.access_token) : Promise.resolve(allTags),
        allFeatures.length === 0 ? getFeatures(session?.access_token) : Promise.resolve(allFeatures),
      ]);

      if (allEntityTypes.length === 0 && Array.isArray(typesResult)) setAllEntityTypes(typesResult as EntityType[]);
      if (allCategories.length === 0 && Array.isArray(categoriesResult)) setAllCategories(categoriesResult as Category[]);
      if (allTags.length === 0 && Array.isArray(tagsResult)) setAllTags(tagsResult as Tag[]);
      if (allFeatures.length === 0 && Array.isArray(featuresResult)) setAllFeatures(featuresResult as Feature[]);
    } catch (err: unknown) {
      console.error('Failed to fetch filter data:', err);
      // Not setting main error state here, as entity fetching might still work
      // Or, you might want a specific error state for filters
    }
    setIsLoadingFilters(false);
  }, [session, allEntityTypes, allCategories, allTags, allFeatures]);

  const fetchEntitiesData = useCallback(async (isLoadMore = false) => {
    if (!isLoadMore) {
      setIsLoading(true);
      setEntities([]); // Clear entities for new filter/search, but not for load more
    } else {
      setIsLoadingMore(true);
    }
    setError(null);

    // Determine page to fetch
    const pageToFetch = isLoadMore && paginationMeta ? paginationMeta.page + 1 : currentPage;

    try {
      const params: GetEntitiesParams = {
        page: pageToFetch,
        limit: ITEMS_PER_PAGE,
        ...(searchTerm && { searchTerm }),
        ...(selectedEntityTypeIds.length > 0 && { entityTypeIds: selectedEntityTypeIds }),
        ...(selectedCategoryIds.length > 0 && { categoryIds: selectedCategoryIds }),
        ...(selectedTagIds.length > 0 && { tagIds: selectedTagIds }),
        ...(selectedFeatureIds.length > 0 && { featureIds: selectedFeatureIds }),
        sortBy: sortBy,
        sortOrder: sortOrder,
        // Advanced filters
        ...(hasFreeTier !== undefined && { hasFreeTier }),
        ...(apiAccess !== undefined && { apiAccess }),
        ...(employeeCountRanges && employeeCountRanges.length > 0 && { employeeCountRanges }),
        ...(fundingStages && fundingStages.length > 0 && { fundingStages }),
        ...(pricingModels && pricingModels.length > 0 && { pricingModels }),
        ...(priceRanges && priceRanges.length > 0 && { priceRanges }),
        ...(createdAtFrom && { createdAtFrom }),
        ...(createdAtTo && { createdAtTo }),
        ...(locationSearch && { locationSearch }),
      };

      // Debug: Log what we're about to send to the API
      if (employeeCountRanges) {
        console.log('[DEBUG] employeeCountRanges being sent to API:', employeeCountRanges, 'type:', typeof employeeCountRanges, 'isArray:', Array.isArray(employeeCountRanges));
      }
      console.log('[DEBUG] Full params object being sent to API:', params);

      const entitiesResponse = await getEntities(params, session?.access_token);
      
      setEntities(prevEntities => 
        isLoadMore ? [...prevEntities, ...entitiesResponse.data] : entitiesResponse.data
      );
      setPaginationMeta(entitiesResponse.meta);

    } catch (err: unknown) {
      console.error('Failed to fetch entities:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while fetching data.';
      setError(errorMessage);
      setEntities([]);
      setPaginationMeta(null);
    }
    if (!isLoadMore) {
      setIsLoading(false);
    } else {
      setIsLoadingMore(false);
    }
  }, [
    currentPage, // Still needed for initial load based on URL
    searchTerm,
    selectedEntityTypeIds,
    selectedCategoryIds,
    selectedTagIds,
    selectedFeatureIds, // Added
    sortBy,
    sortOrder,
    session,
    paginationMeta,
    // Advanced filters
    hasFreeTier,
    apiAccess,
    employeeCountRanges,
    fundingStages,
    pricingModels,
    priceRanges,
    createdAtFrom,
    createdAtTo,
    locationSearch,
  ]);

  useEffect(() => {
    fetchFilterData();
  }, [fetchFilterData]);

  useEffect(() => {
    if (!isLoadingFilters) { // Fetch entities only after filter data attempts to load
        fetchEntitiesData();
    }
  }, [fetchEntitiesData, isLoadingFilters]);

  // Setup IntersectionObserver
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && paginationMeta?.hasNextPage && !isLoading && !isLoadingMore) {
          // console.log('Reached bottom, loading more...');
          fetchEntitiesData(true); // Pass true for loadMore
        }
      },
      { threshold: 1.0 } // Trigger when 100% of the target is visible
    );

    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }

    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
    };
  }, [fetchEntitiesData, paginationMeta, isLoading, isLoadingMore]); // Add dependencies

  // Function to update URL query parameters
  const updateQueryAndNavigate = (newQueryValues: Record<string, string | string[] | null>) => {
    const current = new URLSearchParams(Array.from(searchParams?.entries() || []));
    let resetPage = false;

    // Define which parameters should always be treated as arrays
    const arrayParameters = new Set([
      'entityTypeIds', 'categoryIds', 'tagIds', 'featureIds',
      'employeeCountRanges', 'fundingStages', 'pricingModels', 'priceRanges',
      'integrations', 'platforms', 'targetAudience'
    ]);

    Object.entries(newQueryValues).forEach(([key, value]) => {
      current.delete(key); // Always delete first to handle array values correctly
      if (value !== null && value !== '' && (!Array.isArray(value) || value.length > 0)) {
        if (Array.isArray(value)) {
          value.forEach(v => current.append(key, v));
        } else if (arrayParameters.has(key)) {
          // For array parameters, always use append even for single values
          current.append(key, value as string);
        } else {
          current.set(key, value as string);
        }
      }
      // Reset page if any filter or search term changes, but not if only page itself changes
      if (key !== 'page' && !(key === 'sortBy' && newQueryValues['sortBy'] === sortBy) && !(key === 'sortOrder' && newQueryValues['sortOrder'] === sortOrder)) {
        resetPage = true;
      }
    });

    if (resetPage) {
      current.set('page', '1');
    }

    router.push(`${pathname}?${current.toString()}`, { scroll: false });
  };

  const handleSearchSubmit = (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    updateQueryAndNavigate({ searchTerm: localSearchInput });
  };

  const handleEntityTypeToggle = (typeId: string) => {
    const newSelectedIds = selectedEntityTypeIds.includes(typeId)
      ? selectedEntityTypeIds.filter(id => id !== typeId)
      : [...selectedEntityTypeIds, typeId];
    updateQueryAndNavigate({ entityTypeIds: newSelectedIds });
  };

  const handleCategoryToggle = (categoryId: string) => { // New handler
    const newSelectedIds = selectedCategoryIds.includes(categoryId)
      ? selectedCategoryIds.filter(id => id !== categoryId)
      : [...selectedCategoryIds, categoryId];
    updateQueryAndNavigate({ categoryIds: newSelectedIds });
  };

  const handleTagToggle = (tagId: string) => {
    const newSelectedIds = selectedTagIds.includes(tagId)
      ? selectedTagIds.filter(id => id !== tagId)
      : [...selectedTagIds, tagId];
    updateQueryAndNavigate({ tagIds: newSelectedIds });
  };

  const handleFeatureToggle = (featureId: string) => {
    const newSelectedIds = selectedFeatureIds.includes(featureId)
      ? selectedFeatureIds.filter(id => id !== featureId)
      : [...selectedFeatureIds, featureId];
    updateQueryAndNavigate({ featureIds: newSelectedIds });
  };

  const handleAdvancedFilterChange = (filterName: string, value: boolean | string | string[] | null) => {
    // Handle different value types for URL params
    let urlValue: string | string[] | null = null;

    if (value === null) {
      urlValue = null;
    } else if (typeof value === 'boolean') {
      urlValue = value ? 'true' : null;
    } else if (Array.isArray(value)) {
      urlValue = value.length > 0 ? value : null;
    } else {
      urlValue = value || null;
    }

    updateQueryAndNavigate({ [filterName]: urlValue });
  };

  const handleClearAdvancedFilters = () => {
    updateQueryAndNavigate({
      hasFreeTier: null,
      apiAccess: null,
      employeeCountRanges: null,
      fundingStages: null,
      pricingModels: null,
      priceRanges: null,
      createdAtFrom: null,
      createdAtTo: null,
      locationSearch: null,
    });
  };

  const handleClearAllFilters = () => {
    setLocalSearchInput(''); // Also clear local search input
    updateQueryAndNavigate({
      searchTerm: null,
      entityTypeIds: null,
      categoryIds: null,
      tagIds: null,
      featureIds: null, // New
      // Advanced filters
      hasFreeTier: null,
      apiAccess: null,
      employeeCountRanges: null,
      fundingStages: null,
      pricingModels: null,
      priceRanges: null,
      createdAtFrom: null,
      createdAtTo: null,
      locationSearch: null,
      page: '1'
    });
  };

  const handleSortByChange = (newSortBy: string) => {
    updateQueryAndNavigate({ sortBy: newSortBy });
  };



  const getPillName = useCallback((id: string, source: 'entityType' | 'category' | 'tag' | 'feature'): string => {
    if (source === 'entityType') return allEntityTypes.find(t => t.id === id)?.name || id;
    if (source === 'category') return allCategories.find(c => c.id === id)?.name || id;
    if (source === 'tag') return allTags.find(t => t.id === id)?.name || id;
    if (source === 'feature') return allFeatures.find(f => f.id === id)?.name || id;
    return id;
  }, [allEntityTypes, allCategories, allTags, allFeatures]);

  const activeFiltersForPills = useMemo(() => {
    return [
      ...selectedEntityTypeIds.map(id => ({ id, name: getPillName(id, 'entityType'), type: 'entityTypeIds' as const })),
      ...selectedCategoryIds.map(id => ({ id, name: getPillName(id, 'category'), type: 'categoryIds' as const })),
      ...selectedTagIds.map(id => ({ id, name: getPillName(id, 'tag'), type: 'tagIds' as const })),
      ...selectedFeatureIds.map(id => ({ id, name: getPillName(id, 'feature'), type: 'featureIds' as const })),
    ];
  }, [selectedEntityTypeIds, selectedCategoryIds, selectedTagIds, selectedFeatureIds, getPillName]);

  const handleRemovePill = (id: string, type: 'entityTypeIds' | 'categoryIds' | 'tagIds' | 'featureIds') => {
    if (type === 'entityTypeIds') handleEntityTypeToggle(id);
    if (type === 'categoryIds') handleCategoryToggle(id);
    if (type === 'tagIds') handleTagToggle(id);
    if (type === 'featureIds') handleFeatureToggle(id);
  };



  // Remove early returns - handle all states within the main layout

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Discover AI Tools & Resources
            </h1>
            <p className="text-xl text-gray-600">
              Explore the latest AI tools, platforms, and resources to supercharge your workflow
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Mobile Filter Toggle */}
        <div className="lg:hidden mb-6">
          <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="inline-flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filters
                {(selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length) > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[320px] overflow-y-auto">
              <div className="space-y-6 pt-6">
                <SmartFilterSection
                  title="Resource Type"
                  items={allEntityTypes}
                  selectedIds={selectedEntityTypeIds}
                  onToggle={handleEntityTypeToggle}
                  isLoading={isLoadingFilters}
                  defaultExpanded={true}
                />

                <SmartFilterSection
                  title="Category"
                  items={allCategories}
                  selectedIds={selectedCategoryIds}
                  onToggle={handleCategoryToggle}
                  isLoading={isLoadingFilters}
                />

                <SmartFilterSection
                  title="Features"
                  items={allFeatures}
                  selectedIds={selectedFeatureIds}
                  onToggle={handleFeatureToggle}
                  isLoading={isLoadingFilters}
                />

                <SmartFilterSection
                  title="Tags"
                  items={allTags}
                  selectedIds={selectedTagIds}
                  onToggle={handleTagToggle}
                  isLoading={isLoadingFilters}
                />

                <AdvancedFilters
                  hasFreeTier={hasFreeTier}
                  apiAccess={apiAccess}
                  employeeCountRanges={employeeCountRanges}
                  fundingStages={fundingStages}
                  pricingModels={pricingModels}
                  priceRanges={priceRanges}
                  createdAtFrom={createdAtFrom}
                  createdAtTo={createdAtTo}
                  locationSearch={locationSearch}
                  onFilterChange={handleAdvancedFilterChange}
                  onClearAdvanced={handleClearAdvancedFilters}
                />
              </div>
            </SheetContent>
          </Sheet>
        </div>

        <div className="lg:flex lg:gap-8">
          {/* Desktop Filter Sidebar */}
          <aside className="hidden lg:block w-80 flex-shrink-0">
            <div className="sticky top-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Filters</h2>

              <SmartFilterSection
                title="Resource Type"
                items={allEntityTypes}
                selectedIds={selectedEntityTypeIds}
                onToggle={handleEntityTypeToggle}
                isLoading={isLoadingFilters}
                defaultExpanded={true}
              />

              <SmartFilterSection
                title="Category"
                items={allCategories}
                selectedIds={selectedCategoryIds}
                onToggle={handleCategoryToggle}
                isLoading={isLoadingFilters}
              />

              <SmartFilterSection
                title="Features"
                items={allFeatures}
                selectedIds={selectedFeatureIds}
                onToggle={handleFeatureToggle}
                isLoading={isLoadingFilters}
              />

              <SmartFilterSection
                title="Tags"
                items={allTags}
                selectedIds={selectedTagIds}
                onToggle={handleTagToggle}
                isLoading={isLoadingFilters}
              />

              <AdvancedFilters
                hasFreeTier={hasFreeTier}
                apiAccess={apiAccess}
                employeeCountRanges={employeeCountRanges}
                fundingStages={fundingStages}
                pricingModels={pricingModels}
                priceRanges={priceRanges}
                createdAtFrom={createdAtFrom}
                createdAtTo={createdAtTo}
                locationSearch={locationSearch}
                onFilterChange={handleAdvancedFilterChange}
                onClearAdvanced={handleClearAdvancedFilters}
              />
            </div>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 space-y-6">
            {/* Search Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <form onSubmit={handleSearchSubmit} className="space-y-4">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="search-input"
                    type="search"
                    value={localSearchInput}
                    onChange={(e) => setLocalSearchInput(e.target.value)}
                    placeholder="Search AI tools, platforms, and resources..."
                    className="pl-10 h-12 text-base border-gray-200 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div className="text-sm text-gray-600">
                    {paginationMeta && paginationMeta.totalItems ? (
                      <span>
                        <span className="font-medium text-gray-900">{paginationMeta.totalItems}</span> resources found
                      </span>
                    ) : (
                      'Loading...'
                    )}
                  </div>

                  <Select value={sortBy} onValueChange={handleSortByChange}>
                    <SelectTrigger className="w-full sm:w-auto min-w-[200px] border-gray-200">
                      <SelectValue placeholder="Sort by..." />
                    </SelectTrigger>
                    <SelectContent>
                      {SORT_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </form>
            </div>

            {/* Active Filter Pills */}
            <ActiveFilters
              filters={activeFiltersForPills}
              onRemove={handleRemovePill}
              onClearAll={handleClearAllFilters}
              searchTerm={searchTerm}
              onClearSearch={() => {
                setLocalSearchInput('');
                updateQueryAndNavigate({ searchTerm: null });
              }}
            />


            {/* Content Display Area */}
            {isLoading && entities.length === 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(ITEMS_PER_PAGE)].map((_, index) => (
                  <ResourceCardSkeleton key={`skeleton-${index}`} />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-10 bg-red-50 dark:bg-red-900/30 p-6 rounded-lg shadow">
                <p className="text-lg text-red-600 dark:text-red-400 mb-3">Error: {error}</p>
                <Button onClick={() => { fetchFilterData(); fetchEntitiesData();}} variant="destructive">
                  Try Again
                </Button>
              </div>
            ) : null}

            {/* Results Section */}
            {!isLoading && !error && entities.length === 0 ? (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <SearchIcon className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No resources found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    We couldn&apos;t find any resources matching your criteria. Try adjusting your search or filters above.
                  </p>

                  {/* Helpful suggestions */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Try these suggestions:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Check your spelling or try different keywords</li>
                      <li>• Remove some filters to broaden your search</li>
                      <li>• Try searching for broader terms (e.g., &quot;AI&quot; instead of &quot;machine learning&quot;)</li>
                      <li>• Browse all resources by clearing filters</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      onClick={handleClearAllFilters}
                      className="text-sm"
                    >
                      Clear all filters & search
                    </Button>
                    <p className="text-xs text-gray-500">
                      Search and filters remain active above for easy modification
                    </p>
                  </div>
                </div>
              </div>
            ) : !isLoading && !error && entities.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {entities.map((entity) => (
                    <ResourceCard
                      key={`entity-${entity.id}`}
                      entity={entity}
                      onBookmark={(entityId) => {
                        // TODO: Implement bookmark functionality
                        console.log('Bookmark entity:', entityId);
                      }}
                      onShare={(entity) => {
                        // TODO: Implement share functionality
                        console.log('Share entity:', entity);
                      }}
                    />
                  ))}
                </div>

                {/* Load More Trigger / Indicator */}
                {paginationMeta?.hasNextPage && (
                  <div ref={loadMoreRef} className="text-center py-10">
                    {isLoadingMore ? (
                      <p className="text-lg text-gray-500 dark:text-gray-400">Loading more resources...</p>
                      // Optionally add a spinner here
                    ) : (
                      // You can have a button here if you prefer a manual "Load More" action initially
                      // <Button onClick={() => fetchEntitiesData(true)}>Load More</Button>
                      <p className="text-sm text-gray-400 dark:text-gray-500">Scroll down to load more.</p>
                    )}
                  </div>
                )}
              </>
            ) : null}
          </main>
        </div>
      </div>
      {/* Overlay for mobile when filter is open */}
      {isMobileFilterOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 lg:hidden"
          onClick={() => setIsMobileFilterOpen(false)}
        ></div>
      )}
    </div>
  );
}

export default function BrowsePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Discover AI Tools & Resources
              </h1>
              <p className="text-xl text-gray-600">
                Loading...
              </p>
            </div>
          </div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-lg text-gray-500">Loading browse page...</p>
          </div>
        </div>
      </div>
    }>
      <BrowsePageContent />
    </Suspense>
  );
}