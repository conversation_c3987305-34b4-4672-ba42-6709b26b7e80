'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronDownIcon, ChevronUpIcon, SlidersHorizontal } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface AdvancedFiltersProps {
  // Boolean filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  mobileSupport?: boolean;
  trialAvailable?: boolean;
  demoAvailable?: boolean;
  openSource?: boolean;
  hasLiveChat?: boolean;
  
  // Enum filters
  pricingModel?: string;
  priceRange?: string;
  learningCurve?: string;
  
  // Handlers
  onFilterChange: (filterName: string, value: boolean | string | null) => void;
  onClearAdvanced: () => void;
}

const PRICING_MODELS = [
  { value: 'FREE', label: 'Free' },
  { value: 'FREEMIUM', label: 'Freemium' },
  { value: 'PAID', label: 'Paid' },
  { value: 'SUBSCRIPTION', label: 'Subscription' },
  { value: 'ONE_TIME', label: 'One-time Purchase' },
];

const PRICE_RANGES = [
  { value: 'FREE', label: 'Free' },
  { value: 'LOW', label: 'Low ($1-$50/month)' },
  { value: 'MEDIUM', label: 'Medium ($51-$200/month)' },
  { value: 'HIGH', label: 'High ($201-$1000/month)' },
  { value: 'ENTERPRISE', label: 'Enterprise ($1000+/month)' },
];

const LEARNING_CURVES = [
  { value: 'LOW', label: 'Easy to Learn' },
  { value: 'MEDIUM', label: 'Moderate Learning' },
  { value: 'HIGH', label: 'Advanced/Complex' },
];

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  hasFreeTier,
  apiAccess,
  mobileSupport,
  trialAvailable,
  demoAvailable,
  openSource,
  hasLiveChat,
  pricingModel,
  priceRange,
  learningCurve,
  onFilterChange,
  onClearAdvanced,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Count active advanced filters
  const activeFiltersCount = [
    hasFreeTier,
    apiAccess,
    mobileSupport,
    trialAvailable,
    demoAvailable,
    openSource,
    hasLiveChat,
    pricingModel,
    priceRange,
    learningCurve,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '').length;

  const handleBooleanChange = (filterName: string, checked: boolean) => {
    onFilterChange(filterName, checked || null);
  };

  const handleSelectChange = (filterName: string, value: string) => {
    onFilterChange(filterName, value === 'all' ? null : value);
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button variant="outline" className="w-full justify-between">
          <div className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            Advanced Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          {isOpen ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="space-y-4 mt-4">
        {/* Boolean Filters */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">Features & Availability</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasFreeTier"
                checked={hasFreeTier || false}
                onCheckedChange={(checked) => handleBooleanChange('hasFreeTier', checked as boolean)}
              />
              <Label htmlFor="hasFreeTier" className="text-sm">Has Free Tier</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="apiAccess"
                checked={apiAccess || false}
                onCheckedChange={(checked) => handleBooleanChange('apiAccess', checked as boolean)}
              />
              <Label htmlFor="apiAccess" className="text-sm">API Access</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="mobileSupport"
                checked={mobileSupport || false}
                onCheckedChange={(checked) => handleBooleanChange('mobileSupport', checked as boolean)}
              />
              <Label htmlFor="mobileSupport" className="text-sm">Mobile Support</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="trialAvailable"
                checked={trialAvailable || false}
                onCheckedChange={(checked) => handleBooleanChange('trialAvailable', checked as boolean)}
              />
              <Label htmlFor="trialAvailable" className="text-sm">Free Trial</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="demoAvailable"
                checked={demoAvailable || false}
                onCheckedChange={(checked) => handleBooleanChange('demoAvailable', checked as boolean)}
              />
              <Label htmlFor="demoAvailable" className="text-sm">Demo Available</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="openSource"
                checked={openSource || false}
                onCheckedChange={(checked) => handleBooleanChange('openSource', checked as boolean)}
              />
              <Label htmlFor="openSource" className="text-sm">Open Source</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasLiveChat"
                checked={hasLiveChat || false}
                onCheckedChange={(checked) => handleBooleanChange('hasLiveChat', checked as boolean)}
              />
              <Label htmlFor="hasLiveChat" className="text-sm">Live Chat Support</Label>
            </div>
          </div>
        </div>

        {/* Enum Filters */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">Pricing & Complexity</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label className="text-sm">Pricing Model</Label>
              <Select value={pricingModel || 'all'} onValueChange={(value) => handleSelectChange('pricingModel', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any</SelectItem>
                  {PRICING_MODELS.map(model => (
                    <SelectItem key={model.value} value={model.value}>
                      {model.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm">Price Range</Label>
              <Select value={priceRange || 'all'} onValueChange={(value) => handleSelectChange('priceRange', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any</SelectItem>
                  {PRICE_RANGES.map(range => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm">Learning Curve</Label>
              <Select value={learningCurve || 'all'} onValueChange={(value) => handleSelectChange('learningCurve', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any</SelectItem>
                  {LEARNING_CURVES.map(curve => (
                    <SelectItem key={curve.value} value={curve.value}>
                      {curve.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Clear Button */}
        {activeFiltersCount > 0 && (
          <div className="pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={onClearAdvanced}
              className="text-xs"
            >
              Clear Advanced Filters
            </Button>
          </div>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default AdvancedFilters;
